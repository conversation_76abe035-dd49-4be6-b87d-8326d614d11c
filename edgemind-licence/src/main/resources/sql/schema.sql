/*
 Navicat Premium Data Transfer

 Source Server         : local-mysql8
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : wkg0502

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 11/05/2025 23:45:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for chat_conversation
-- ----------------------------
DROP TABLE IF EXISTS `chat_conversation`;
CREATE TABLE `chat_conversation`  (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会话ID',
                                      `user_id` bigint NOT NULL COMMENT '用户ID',
                                      `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话标题',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_time` datetime NOT NULL COMMENT '最后更新时间',
                                      `status` tinyint NULL DEFAULT 1 COMMENT '状态 1-正常 0-已删除',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      INDEX `idx_user_id`(`user_id` ASC) USING BTREE COMMENT '用户ID索引',
                                      INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引',
                                      CONSTRAINT `chat_conversation_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 138 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天会话表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_conversation
-- ----------------------------

-- ----------------------------
-- Table structure for chat_conversation_tag
-- ----------------------------
DROP TABLE IF EXISTS `chat_conversation_tag`;
CREATE TABLE `chat_conversation_tag`  (
                                          `conversation_id` bigint NOT NULL COMMENT '会话ID',
                                          `tag_id` bigint NOT NULL COMMENT '标签ID',
                                          PRIMARY KEY (`conversation_id`, `tag_id`) USING BTREE COMMENT '复合主键',
                                          INDEX `tag_id`(`tag_id` ASC) USING BTREE,
                                          CONSTRAINT `chat_conversation_tag_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `chat_conversation` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
                                          CONSTRAINT `chat_conversation_tag_ibfk_2` FOREIGN KEY (`tag_id`) REFERENCES `chat_tag` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '会话标签关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_conversation_tag
-- ----------------------------

-- ----------------------------
-- Table structure for chat_message
-- ----------------------------
DROP TABLE IF EXISTS `chat_message`;
CREATE TABLE `chat_message`  (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
                                 `conversation_id` bigint NOT NULL COMMENT '会话ID',
                                 `sender` enum('user','ai') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发送者类型',
                                 `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
                                 `thinking_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'AI思考过程内容',
                                 `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片路径(如果有)',
                                 `token_count` int NULL DEFAULT 0 COMMENT '消息Token数量',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `model_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '使用的模型名称',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 INDEX `idx_conversation_id`(`conversation_id` ASC) USING BTREE COMMENT '会话ID索引',
                                 INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引',
                                 CONSTRAINT `chat_message_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `chat_conversation` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 796 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_message
-- ----------------------------

-- ----------------------------
-- Table structure for chat_tag
-- ----------------------------
DROP TABLE IF EXISTS `chat_tag`;
CREATE TABLE `chat_tag`  (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
                             `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
                             `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '#1a73e8' COMMENT '标签颜色',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `uk_tag_name`(`name` ASC) USING BTREE COMMENT '标签名唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_tag
-- ----------------------------

-- ----------------------------
-- Table structure for kb_document_chunks
-- ----------------------------
DROP TABLE IF EXISTS `kb_document_chunks`;
CREATE TABLE `kb_document_chunks`  (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `document_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的文档ID',
                                       `node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的节点ID',
                                       `chunk_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '向量存储中的分片ID',
                                       `chunk_index` int NOT NULL COMMENT '分片索引号',
                                       `chunk_size` int NOT NULL COMMENT '分片文本长度',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       INDEX `idx_document_chunks_doc_id`(`document_id` ASC) USING BTREE,
                                       INDEX `idx_document_chunks_node_id`(`node_id` ASC) USING BTREE,
                                       INDEX `idx_document_chunks_chunk_id`(`chunk_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9751 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文档分片映射表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_document_chunks
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_documents
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_documents`;
CREATE TABLE `kb_knowledge_documents`  (
                                           `document_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档ID (UUID)',
                                           `node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的节点 ID',
                                           `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件MIME类型',
                                           `file_size_bytes` bigint NULL DEFAULT NULL COMMENT '文件大小（字节）',
                                           `vector_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'PENDING' COMMENT '向量化状态: PENDING, PROCESSING, INDEXED, FAILED',
                                           `vector_error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '记录向量化失败的原因',
                                           `last_indexed_time` datetime NULL DEFAULT NULL COMMENT '最后成功索引的时间',
                                           `content_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件内容的哈希值',
                                           `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`document_id`) USING BTREE,
                                           UNIQUE INDEX `uq_kb_docs_node_id`(`node_id` ASC) USING BTREE COMMENT '一个节点关联一个文档',
                                           INDEX `idx_kb_docs_vector_status`(`vector_status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文档内容引用和向量化状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_documents
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_node_closure
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_node_closure`;
CREATE TABLE `kb_knowledge_node_closure`  (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                              `ancestor_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '祖先节点ID',
                                              `descendant_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '后代节点ID',
                                              `depth` int NOT NULL COMMENT '层级深度 (0表示自身)',
                                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              UNIQUE INDEX `uq_kb_node_closure_rel`(`ancestor_id` ASC, `descendant_id` ASC) USING BTREE,
                                              INDEX `idx_kb_node_closure_ancestor`(`ancestor_id` ASC) USING BTREE,
                                              INDEX `idx_kb_node_closure_descendant`(`descendant_id` ASC) USING BTREE,
                                              INDEX `idx_kb_node_closure_depth`(`depth` ASC) USING BTREE,
                                              CONSTRAINT `fk_kb_node_closure_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `kb_knowledge_nodes` (`node_id`) ON DELETE CASCADE ON UPDATE CASCADE,
                                              CONSTRAINT `fk_kb_node_closure_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `kb_knowledge_nodes` (`node_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 447 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库节点闭包表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_node_closure
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_nodes
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_nodes`;
CREATE TABLE `kb_knowledge_nodes`  (
                                       `node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点ID (UUID)',
                                       `space_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属知识空间 ID',
                                       `parent_node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父节点 ID，根节点为 NULL',
                                       `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点名称',
                                       `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点类型: FOLDER, FILE',
                                       `create_by` bigint NULL DEFAULT NULL COMMENT '创建者用户ID (sys_user.id)',
                                       `update_by` bigint NULL DEFAULT NULL COMMENT '最后更新者用户ID (sys_user.id)',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`node_id`) USING BTREE,
                                       UNIQUE INDEX `uq_kb_nodes_parent_name`(`space_id` ASC, `parent_node_id` ASC, `name` ASC) USING BTREE COMMENT '同一父节点下名称唯一 (限制 parent_node_id 索引长度避免超长)',
                                       INDEX `idx_kb_nodes_space_id`(`space_id` ASC) USING BTREE,
                                       INDEX `idx_kb_nodes_parent_id`(`parent_node_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库节点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_nodes
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_spaces
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_spaces`;
CREATE TABLE `kb_knowledge_spaces`  (
                                        `space_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识空间ID (UUID)',
                                        `owner_user_id` bigint NULL DEFAULT NULL COMMENT '私人空间的所有者 ID (sys_user.id)，团队空间时为 NULL',
                                        `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '空间名称',
                                        `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '空间描述',
                                        `is_private` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为私人空间 (1=私人, 0=团队)',
                                        `create_by` bigint NULL DEFAULT NULL COMMENT '创建者用户ID (sys_user.id)',
                                        `update_by` bigint NULL DEFAULT NULL COMMENT '最后更新者用户ID (sys_user.id)',
                                        `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`space_id`) USING BTREE,
                                        UNIQUE INDEX `uq_kb_spaces_owner`(`owner_user_id` ASC) USING BTREE COMMENT '确保每个用户最多一个私人空间',
                                        INDEX `idx_kb_spaces_owner`(`owner_user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识空间表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_spaces
-- ----------------------------
INSERT INTO `kb_knowledge_spaces` VALUES ('956ed750-9629-4734-97af-6f8903ab8564', 1, '个人库', '个人私有知识空间', 1, 1, 1, '2025-05-02 16:35:38', '2025-05-02 16:35:38');
INSERT INTO `kb_knowledge_spaces` VALUES ('9b57d802-c4e5-40be-831e-d394f870baa6', NULL, '知识库', '公共团队知识空间', 0, 1, 1, '2025-05-02 16:32:08', '2025-05-02 16:32:08');

-- ----------------------------
-- Table structure for sys_department
-- ----------------------------
DROP TABLE IF EXISTS `sys_department`;
CREATE TABLE `sys_department`  (
                                   `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '部门ID',
                                   `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门名称',
                                   `dept_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门编码',
                                   `parent_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '父部门ID (0表示顶级)',
                                   `manager_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '部门负责人ID',
                                   `sort_order` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '显示顺序',
                                   `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 (0: 停用, 1: 正常)',
                                   `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门描述/职责',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                   `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                   `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_dept_code`(`dept_code` ASC) USING BTREE,
                                   INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
                                   INDEX `idx_status`(`status` ASC) USING BTREE,
                                   INDEX `idx_manager_id`(`manager_id` ASC) USING BTREE,
                                   INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '部门结构表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_department
-- ----------------------------
INSERT INTO `sys_department` VALUES (1, '总部', NULL, 0, NULL, 0, 1, NULL, '2025-04-26 12:56:11', '2025-04-26 12:56:11');

-- ----------------------------
-- Table structure for sys_license
-- ----------------------------
DROP TABLE IF EXISTS `sys_license`;
CREATE TABLE `sys_license`  (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '许可证ID',
                                `license_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '许可证密钥',
                                `hardware_fingerprint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件指纹',
                                `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '激活状态：0-未激活，1-已激活',
                                `activated_time` datetime NULL DEFAULT NULL COMMENT '激活时间',
                                `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间，NULL表示永不过期',
                                `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统许可证表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_license
-- ----------------------------

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
                                   `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '权限ID',
                                   `permission_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
                                   `permission_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限编码 (用于权限控制)',
                                   `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限类型 (MENU, BUTTON, API, DATA)',
                                   `parent_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '父权限ID (0表示顶级)',
                                   `resource_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源URL',
                                   `http_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'HTTP方法',
                                   `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
                                   `sort_order` int DEFAULT '0' COMMENT '排序',
                                   `is_external` tinyint(1) DEFAULT '0' COMMENT '是否外部链接',
                                   `component_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
                                   `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '权限描述',
                                   `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 (0: 禁用, 1: 启用)',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                   `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                   `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_permission_code`(`permission_code` ASC) USING BTREE,
                                   INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
                                   INDEX `idx_type`(`type` ASC) USING BTREE,
                                   INDEX `idx_status`(`status` ASC) USING BTREE,
                                   INDEX `idx_permission_code`(`permission_code` ASC) USING BTREE,
                                   INDEX `idx_type_status`(`type`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '权限信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_permission
-- ----------------------------
INSERT INTO `sys_permission` VALUES (1, 'AI对话', 'menu:view:ai_chat', 'MENU', 0, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (2, 'AI知识库', 'menu:view:ai_knowledge', 'MENU', 0, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (3, 'AI工具', 'menu:view:ai_tools', 'MENU', 0, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (4, '权限管理', 'menu:view:permission_group', 'MENU', 0, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (5, '用户管理', 'menu:view:users', 'MENU', 4, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (6, '部门管理', 'menu:view:departments', 'MENU', 4, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (7, '角色管理', 'menu:view:roles', 'MENU', 4, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (8, '权限设置', 'menu:view:permissions', 'MENU', 4, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (9, '操作日志', 'menu:view:logs', 'MENU', 4, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (10, '查询用户列表', 'user:manage:list', 'API', 5, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (11, '新增用户', 'user:manage:create', 'API', 5, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (12, '修改用户', 'user:manage:update', 'API', 5, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (13, '删除用户', 'user:manage:delete', 'API', 5, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (14, '分配用户角色', 'user:manage:assign_role', 'API', 5, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (15, '重置用户密码', 'user:manage:reset_password', 'API', 5, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (16, '查询角色列表', 'role:manage:list', 'API', 7, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (17, '新增角色', 'role:manage:create', 'API', 7, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (18, '修改角色', 'role:manage:update', 'API', 7, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (19, '删除角色', 'role:manage:delete', 'API', 7, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (20, '分配角色权限', 'role:manage:assign_permission', 'API', 7, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (21, '查询权限列表', 'perm:manage:list', 'API', 8, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (22, '新增权限', 'perm:manage:create', 'API', 8, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (23, '修改权限', 'perm:manage:update', 'API', 8, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (24, '删除权限', 'perm:manage:delete', 'API', 8, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (25, '查询部门列表/树', 'dept:manage:list', 'API', 6, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (26, '新增部门', 'dept:manage:create', 'API', 6, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (27, '修改部门', 'dept:manage:update', 'API', 6, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');
INSERT INTO `sys_permission` VALUES (28, '删除部门', 'dept:manage:delete', 'API', 6, NULL, 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
                             `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色ID',
                             `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
                             `role_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色编码 (用于权限控制)',
                             `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色描述',
                             `data_scope` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'DEPT' COMMENT '数据权限范围',
                             `dept_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '数据权限部门ID列表',
                             `sort_order` int DEFAULT '0' COMMENT '排序',
                             `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 (0: 禁用, 1: 启用)',
                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `create_by` bigint DEFAULT NULL COMMENT '创建人',
                             `update_by` bigint DEFAULT NULL COMMENT '更新人',
                             `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `uk_role_code`(`role_code` ASC) USING BTREE,
                             INDEX `idx_status`(`status` ASC) USING BTREE,
                             INDEX `idx_role_code`(`role_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', '拥有系统所有权限', 1, '2025-04-26 12:56:30', '2025-04-26 12:56:30');
INSERT INTO `sys_role` VALUES (2, '普通用户', 'user', '拥有基本访问和操作权限', 1, '2025-04-26 12:56:30', '2025-04-26 12:56:30');

-- ----------------------------
-- Table structure for sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission`  (
                                        `role_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
                                        `permission_id` bigint UNSIGNED NOT NULL COMMENT '权限ID',
                                        PRIMARY KEY (`role_id`, `permission_id`) USING BTREE,
                                        INDEX `idx_rp_permission_id`(`permission_id` ASC) USING BTREE,
                                        INDEX `idx_rp_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色权限关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_permission
-- ----------------------------
INSERT INTO `sys_role_permission` VALUES (1, 1);
INSERT INTO `sys_role_permission` VALUES (2, 1);
INSERT INTO `sys_role_permission` VALUES (1, 2);
INSERT INTO `sys_role_permission` VALUES (2, 2);
INSERT INTO `sys_role_permission` VALUES (1, 3);
INSERT INTO `sys_role_permission` VALUES (2, 3);
INSERT INTO `sys_role_permission` VALUES (1, 4);
INSERT INTO `sys_role_permission` VALUES (1, 5);
INSERT INTO `sys_role_permission` VALUES (1, 6);
INSERT INTO `sys_role_permission` VALUES (1, 7);
INSERT INTO `sys_role_permission` VALUES (1, 8);
INSERT INTO `sys_role_permission` VALUES (1, 9);
INSERT INTO `sys_role_permission` VALUES (1, 10);
INSERT INTO `sys_role_permission` VALUES (1, 11);
INSERT INTO `sys_role_permission` VALUES (1, 12);
INSERT INTO `sys_role_permission` VALUES (1, 13);
INSERT INTO `sys_role_permission` VALUES (1, 14);
INSERT INTO `sys_role_permission` VALUES (1, 15);
INSERT INTO `sys_role_permission` VALUES (1, 16);
INSERT INTO `sys_role_permission` VALUES (1, 17);
INSERT INTO `sys_role_permission` VALUES (1, 18);
INSERT INTO `sys_role_permission` VALUES (1, 19);
INSERT INTO `sys_role_permission` VALUES (1, 20);
INSERT INTO `sys_role_permission` VALUES (1, 21);
INSERT INTO `sys_role_permission` VALUES (1, 22);
INSERT INTO `sys_role_permission` VALUES (1, 23);
INSERT INTO `sys_role_permission` VALUES (1, 24);
INSERT INTO `sys_role_permission` VALUES (1, 25);
INSERT INTO `sys_role_permission` VALUES (1, 26);
INSERT INTO `sys_role_permission` VALUES (1, 27);
INSERT INTO `sys_role_permission` VALUES (1, 28);

-- ----------------------------
-- Table structure for sys_settings
-- ----------------------------
DROP TABLE IF EXISTS `sys_settings`;
CREATE TABLE `sys_settings`  (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
                                 `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设置键名',
                                 `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '设置值',
                                 `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置描述',
                                 `setting_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置分组',
                                 `setting_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'STRING' COMMENT '设置类型',
                                 `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统内置',
                                 `sort_order` int DEFAULT '0' COMMENT '排序',
                                 `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                 `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 UNIQUE INDEX `uk_setting_key`(`setting_key` ASC) USING BTREE,
                                 INDEX `idx_setting_group`(`setting_group` ASC) USING BTREE,
                                 INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_settings
-- ----------------------------
INSERT INTO `sys_settings` (`setting_key`, `setting_value`, `description`, `setting_group`, `setting_type`, `is_system`, `sort_order`, `status`) VALUES
('system.name', 'EdgeMind智能知识管理系统', '系统名称', 'SYSTEM', 'STRING', 1, 1, 1),
('system.version', '1.0.0', '系统版本', 'SYSTEM', 'STRING', 1, 2, 1),
('system.logo', '/wkg/images/logo.png', '系统Logo', 'APPEARANCE', 'IMAGE', 1, 1, 1),
('system.favicon', '/wkg/images/favicon.ico', '网站图标', 'APPEARANCE', 'IMAGE', 1, 2, 1),
('security.password.min_length', '6', '密码最小长度', 'SECURITY', 'NUMBER', 1, 1, 1),
('security.password.require_special', 'false', '密码是否需要特殊字符', 'SECURITY', 'BOOLEAN', 1, 2, 1),
('security.login.max_attempts', '5', '登录最大尝试次数', 'SECURITY', 'NUMBER', 1, 3, 1),
('security.session.timeout', '7200', '会话超时时间（秒）', 'SECURITY', 'NUMBER', 1, 4, 1),
('log.operation.retention_days', '90', '操作日志保留天数', 'LOG', 'NUMBER', 1, 1, 1),
('log.operation.auto_cleanup', 'true', '是否自动清理过期日志', 'LOG', 'BOOLEAN', 1, 2, 1),
('notification.email.enabled', 'false', '是否启用邮件通知', 'NOTIFICATION', 'BOOLEAN', 1, 1, 1),
('notification.sms.enabled', 'false', '是否启用短信通知', 'NOTIFICATION', 'BOOLEAN', 1, 2, 1);

-- ----------------------------
-- Table structure for sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
    `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作用户名',
    `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作模块',
    `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
    `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作描述',
    `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求方法',
    `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求URL',
    `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数',
    `response_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '响应结果',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '操作状态：0-失败，1-成功',
    `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '错误信息',
    `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作IP',
    `user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户代理',
    `execution_time` bigint DEFAULT NULL COMMENT '执行时间（毫秒）',
    `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE,
    KEY `idx_username` (`username`) USING BTREE,
    KEY `idx_module` (`module`) USING BTREE,
    KEY `idx_operation_type` (`operation_type`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_operation_time` (`operation_time`) USING BTREE,
    KEY `idx_ip_address` (`ip_address`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志表' ROW_FORMAT=Dynamic;

-- ----------------------------
-- Records of sys_operation_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '用户ID',
    `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
    `login_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'WEB' COMMENT '登录类型：WEB,MOBILE,API',
    `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录IP',
    `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录地点',
    `browser` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '浏览器',
    `os` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作系统',
    `device_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备类型：Desktop,Mobile,Tablet',
    `user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户代理',
    `login_status` tinyint NOT NULL COMMENT '登录状态：0-失败，1-成功',
    `failure_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '失败原因',
    `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会话ID',
    `token_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Token值',
    `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `logout_time` datetime DEFAULT NULL COMMENT '登出时间',
    `online_duration` bigint DEFAULT NULL COMMENT '在线时长（分钟）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE,
    KEY `idx_username` (`username`) USING BTREE,
    KEY `idx_login_status` (`login_status`) USING BTREE,
    KEY `idx_login_time` (`login_time`) USING BTREE,
    KEY `idx_ip_address` (`ip_address`) USING BTREE,
    KEY `idx_session_id` (`session_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='登录日志表' ROW_FORMAT=Dynamic;

-- ----------------------------
-- Records of sys_login_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
                             `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码 (存储加密后的值)',
                             `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户昵称/姓名',
                             `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
                             `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
                             `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
                             `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户状态 (0: 禁用, 1: 启用)',
                             `dept_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '所属部门ID',
                             `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                             `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
                             `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
                             `password_update_time` datetime DEFAULT NULL COMMENT '密码更新时间',
                             `account_locked` tinyint(1) DEFAULT '0' COMMENT '账号是否锁定',
                             `lock_time` datetime DEFAULT NULL COMMENT '锁定时间',
                             `failed_login_attempts` int DEFAULT '0' COMMENT '登录失败次数',
                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `create_by` bigint DEFAULT NULL COMMENT '创建人',
                             `update_by` bigint DEFAULT NULL COMMENT '更新人',
                             `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `uk_username`(`username` ASC) USING BTREE,
                             INDEX `idx_username`(`username` ASC) USING BTREE,
                             INDEX `idx_status`(`status` ASC) USING BTREE,
                             INDEX `idx_dept_id`(`dept_id` ASC) USING BTREE,
                             INDEX `idx_email`(`email` ASC) USING BTREE,
                             INDEX `idx_phone`(`phone` ASC) USING BTREE,
                             INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', '管理员', '<EMAIL>', '13800000000', 1, 1, '初始超级管理员', '2025-04-26 12:57:48', '2025-04-26 12:57:48');

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
                                  `user_id` bigint NOT NULL COMMENT '用户ID',
                                  `role_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
                                  PRIMARY KEY (`user_id`, `role_id`) USING BTREE,
                                  INDEX `idx_ur_role_id`(`role_id` ASC) USING BTREE,
                                  INDEX `idx_ur_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);

-- ----------------------------
-- Table structure for user_preference
-- ----------------------------
DROP TABLE IF EXISTS `user_preference`;
CREATE TABLE `user_preference`  (
                                    `user_id` bigint NOT NULL COMMENT '用户ID',
                                    `preferred_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '偏好的模型',
                                    `theme` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'light' COMMENT '主题偏好',
                                    `display_thinking` tinyint(1) NULL DEFAULT 1 COMMENT '是否显示思考过程',
                                    `last_active_conversation_id` bigint NULL DEFAULT NULL COMMENT '最后活动的会话ID',
                                    `update_time` datetime NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`user_id`) USING BTREE,
                                    INDEX `last_active_conversation_id`(`last_active_conversation_id` ASC) USING BTREE,
                                    CONSTRAINT `user_preference_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
                                    CONSTRAINT `user_preference_ibfk_2` FOREIGN KEY (`last_active_conversation_id`) REFERENCES `chat_conversation` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户偏好设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_preference
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
